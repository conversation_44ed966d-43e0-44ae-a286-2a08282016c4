package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// CreateFormulaCards godoc
//
//	@Summary		CreateFormulaCards
//	@Description	create multiple formula cards for a subject
//	@Security       BearerAuth
//	@Param			item	body	models.FormulaCardsForCreate	true	"formula cards details"
//	@Tags			formula-cards
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.FormulaCard
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/formula-cards [post]
func (h *Handlers) CreateFormulaCards(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("CreateFormulaCards request started", "client_ip", clientIP)

	formulaCardsInput := new(models.FormulaCardsForCreate)
	if err := ctx.ShouldBindJSON(formulaCardsInput); err != nil {
		duration := time.Since(start)
		slog.Warn("CreateFormulaCards failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate input
	if len(formulaCardsInput.FormulaCards) == 0 {
		duration := time.Since(start)
		slog.Warn("CreateFormulaCards failed - no formula cards provided",
			"client_ip", clientIP,
			"subject_name", formulaCardsInput.SubjectName,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No formula cards provided"})
		return
	}

	slog.Debug("CreateFormulaCards processing",
		"client_ip", clientIP,
		"subject_name", formulaCardsInput.SubjectName,
		"card_count", len(formulaCardsInput.FormulaCards),
	)

	createdCards, err := h.db.CreateFormulaCards(ctx.Request.Context(), formulaCardsInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("CreateFormulaCards failed - database error",
			"client_ip", clientIP,
			"subject_name", formulaCardsInput.SubjectName,
			"card_count", len(formulaCardsInput.FormulaCards),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)

		// Check for specific error types
		if err.Error() == "subject '"+formulaCardsInput.SubjectName+"' not found: record not found" {
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Subject not found"})
		} else {
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		}
		return
	}

	duration := time.Since(start)
	slog.Info("CreateFormulaCards successful",
		"client_ip", clientIP,
		"subject_name", formulaCardsInput.SubjectName,
		"card_count", len(createdCards),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"formula_cards": createdCards})
}

// GetFormulaCards godoc
//
//	@Summary		Get Formula Cards
//	@Description	get all formula cards organized by subject
//	@Security       BearerAuth
//	@Tags			formula-cards
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.FormulaCardsBySubject
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/formula-cards [get]
func (h *Handlers) GetFormulaCards(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetFormulaCards request started", "client_ip", clientIP)

	// Always return all formula cards organized by subject
	slog.Debug("GetFormulaCards processing for all subjects organized",
		"client_ip", clientIP,
	)

	organizedCards, err := h.db.GetAllFormulaCardsOrganizedBySubject(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetFormulaCards failed - database error for organized cards",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	totalCards := 0
	for _, subjectCards := range organizedCards {
		totalCards += len(subjectCards.FormulaCards)
	}

	slog.Info("GetFormulaCards successful for organized cards",
		"client_ip", clientIP,
		"subject_count", len(organizedCards),
		"total_card_count", totalCards,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"formula_cards_by_subject": organizedCards})
}
