package http

import (
	"log/slog"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"
)

// CreateStudent godoc
//
//		@Summary		CreateStudent
//		@Description	create new student with enhanced information including institute, class, stream, city and state
//	 @Param			item	body	models.StudentForCreate	true	"student details with enhanced fields"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.CreatedStudentResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/students [post]
func (h *Handlers) CreateStudent(ctx *gin.Context) {
	stuInput := new(models.StudentForCreate)
	if err := ctx.ShouldBindJSON(stuInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	slog.Debug("Processing student creation request", "email", stuInput.Email)
	// var stu models.Student
	stu := models.Student{
		User: models.User{
			FullName:       stuInput.FullName,
			Email:          stuInput.Email,
			PhoneNumber:    stuInput.PhoneNumber,
			ContactAddress: stuInput.ContactAddress,
			Role:           "Student",
		},
		ParentPhone: stuInput.ParentPhone,
		ParentEmail: stuInput.ParentEmail,
		// Enhanced student information
		Institute:  stuInput.Institute,
		Class:      stuInput.Class,
		Stream:     stuInput.Stream,
		CityOrTown: stuInput.CityOrTown,
		State:      stuInput.State,
	}

	createdStudent, err := h.db.CreateStudent(ctx.Request.Context(), &stu)
	if err != nil {
		slog.Error("Failed to create student", "email", stuInput.Email,
			"error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Created student", "email", createdStudent.User.Email)
	// Generate JWT token
	token, err := token.GenerateJWT(createdStudent.UserID)
	if err != nil {
		slog.Error("Failed to generate token", "email", stuInput.Email, "error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	slog.Debug("Generated token", "email", stuInput.Email)
	// Return the token to the user
	ctx.JSON(http.StatusOK, gin.H{"token": token, "createdStudent": createdStudent})
}

// EnrollInCourse godoc
//
//		@Summary		EnrollInCourse
//		@Description	enroll student in a course
//	     @Security       BearerAuth
//	 @Param			course_id	path	uint	true	"course ID to enroll in"
//		@Tags			students
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.Student
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/enroll/{course_id} [post]
func (h *Handlers) EnrollInCourse(ctx *gin.Context) {
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	courseIDStr, ok := ctx.Params.Get("course_id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing course_id parameter"})
		return
	}
	courseID, _ := strconv.Atoi(courseIDStr)
	updatedStudent, err := h.db.EnrollStudentInCourse(ctx.Request.Context(),
		userID, uint(courseID))

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// Return the token to the user
	ctx.JSON(http.StatusOK, updatedStudent)
}
