package http

import (
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// CreatePreviousYearPapers godoc
//
//	@Summary		CreatePreviousYearPapers
//	@Description	create multiple previous year papers in bulk
//	@Security       BearerAuth
//	@Param			item	body	models.PreviousYearPapersForCreate	true	"previous year papers details"
//	@Tags			previous-year-papers
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.PreviousYearPaper
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/previous-year-papers [post]
func (h *Handlers) CreatePreviousYearPapers(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("CreatePreviousYearPapers request started", "client_ip", clientIP)

	papersInput := new(models.PreviousYearPapersForCreate)
	if err := ctx.ShouldBindJSON(papersInput); err != nil {
		duration := time.Since(start)
		slog.Warn("CreatePreviousYearPapers failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("CreatePreviousYearPapers request",
		"client_ip", clientIP,
		"paper_count", len(papersInput.Papers),
	)

	// Validate that we have papers to create
	if len(papersInput.Papers) == 0 {
		duration := time.Since(start)
		slog.Warn("CreatePreviousYearPapers failed - no papers provided",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "No papers provided"})
		return
	}

	// Additional validation for exam types
	for i, paper := range papersInput.Papers {
		if paper.ExamType != models.ExamTypeIITJEE && paper.ExamType != models.ExamTypeNEET {
			duration := time.Since(start)
			slog.Warn("CreatePreviousYearPapers failed - invalid exam type",
				"client_ip", clientIP,
				"paper_index", i,
				"exam_type", paper.ExamType,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid exam type. Must be either 'IIT-JEE' or 'NEET'",
			})
			return
		}
	}

	createdPapers, err := h.db.CreatePreviousYearPapers(ctx.Request.Context(), papersInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("CreatePreviousYearPapers failed - database error",
			"client_ip", clientIP,
			"paper_count", len(papersInput.Papers),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)

		// Check for specific error types
		if err.Error() == "no previous year papers provided" {
			ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("CreatePreviousYearPapers successful",
		"client_ip", clientIP,
		"paper_count", len(createdPapers),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, createdPapers)
}

// GetAllPreviousYearPapersOrganizedByExamType godoc
//
//	@Summary		Get All Previous Year Papers Organized by Exam Type
//	@Description	get all previous year papers organized by exam type, sorted by year (descending) then month (descending)
//	@Security       BearerAuth
//	@Tags			previous-year-papers
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.PreviousYearPapersByExamType
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/previous-year-papers [get]
func (h *Handlers) GetAllPreviousYearPapersOrganizedByExamType(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetAllPreviousYearPapersOrganizedByExamType request started", "client_ip", clientIP)

	papersByExamType, err := h.db.GetAllPreviousYearPapersOrganizedByExamType(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetAllPreviousYearPapersOrganizedByExamType failed - database error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	totalPapers := 0
	for _, examTypeData := range papersByExamType {
		totalPapers += len(examTypeData.Papers)
	}

	slog.Info("GetAllPreviousYearPapersOrganizedByExamType successful",
		"client_ip", clientIP,
		"exam_types_count", len(papersByExamType),
		"total_papers", totalPapers,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"papers_by_exam_type": papersByExamType})
}
