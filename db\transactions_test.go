package db

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var testDB *gorm.DB
var dbPlugin *DbPlugin

func TestMain(m *testing.M) {
	// Setup test database connection
	dsn := "host=localhost user=postgres password=postgres dbname=mydatabase port=5432 sslmode=disable"
	var err error
	testDB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to test DB: %v", err)
	}

	dbPlugin = NewDbPlugin(testDB)

	// Clean up before tests
	cleanupTransactionTestData()

	code := m.Run()

	// Cleanup after tests
	cleanupTransactionTestData()
	sqlDB, _ := testDB.DB()
	sqlDB.Close()
	os.Exit(code)
}

func cleanupTransactionTestData() {
	// Clean up in order to respect foreign key constraints
	testDB.Exec("DELETE FROM transaction_courses WHERE transaction_id IN (SELECT id FROM transactions WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '<EMAIL>')))")
	testDB.Exec("DELETE FROM transactions WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '<EMAIL>'))")
	testDB.Exec("DELETE FROM students_courses WHERE student_id IN (SELECT id FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '<EMAIL>'))")
	testDB.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email LIKE '<EMAIL>')")
	testDB.Exec("DELETE FROM courses WHERE name LIKE 'Test Transaction%'")
	testDB.Exec("DELETE FROM users WHERE email LIKE '<EMAIL>'")
}

func createTestStudent(t *testing.T) models.Student {
	// Use test name to create unique identifiers
	testName := t.Name()
	email := fmt.Sprintf("<EMAIL>", testName)
	// Create unique phone number based on test name hash
	phoneNumber := fmt.Sprintf("123456%04d", len(testName)%10000)

	user := models.User{
		FullName:    "Test Transaction Student",
		Email:       email,
		PhoneNumber: phoneNumber,
		Role:        "student",
	}
	result := testDB.Create(&user)
	require.NoError(t, result.Error)

	student := models.Student{
		UserID:      user.ID,
		ParentPhone: fmt.Sprintf("098765%04d", len(testName)%10000),
		ParentEmail: fmt.Sprintf("<EMAIL>", testName),
	}
	result = testDB.Create(&student)
	require.NoError(t, result.Error)

	// Load with user association
	testDB.Preload("User").First(&student, student.ID)
	return student
}

func createTestCourses(t *testing.T) (models.Course, models.Course) {
	// Create paid course
	paidCourse := models.Course{
		Name:           "Test Transaction Paid Course",
		Description:    "A paid course for transaction testing",
		Price:          1000,
		Discount:       10.0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     models.CourseTypeIITJEE,
	}
	result := testDB.Create(&paidCourse)
	require.NoError(t, result.Error)

	// Create another paid course
	anotherPaidCourse := models.Course{
		Name:           "Test Transaction Another Paid Course",
		Description:    "Another paid course for transaction testing",
		Price:          2000,
		Discount:       20.0,
		DurationInDays: 60,
		IsFree:         false,
		CourseType:     models.CourseTypeNEET,
	}
	result = testDB.Create(&anotherPaidCourse)
	require.NoError(t, result.Error)

	return paidCourse, anotherPaidCourse
}

func TestCreateTransaction(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, anotherPaidCourse := createTestCourses(t)

	t.Run("Create transaction with single course", func(t *testing.T) {
		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusPending,
			PaymentMethod: "UPI",
		}

		createdTransaction, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID})

		assert.NoError(t, err)
		assert.NotZero(t, createdTransaction.ID)
		assert.Equal(t, student.ID, createdTransaction.StudentID)
		assert.Equal(t, models.TransactionStatusPending, createdTransaction.Status)
		assert.Equal(t, "UPI", createdTransaction.PaymentMethod)
		assert.Equal(t, 900, createdTransaction.Amount) // 1000 - 10% discount
		assert.Len(t, createdTransaction.Courses, 1)
		assert.Equal(t, paidCourse.ID, createdTransaction.Courses[0].ID)
		assert.NotZero(t, createdTransaction.TransactionDate)
	})

	t.Run("Create transaction with multiple courses", func(t *testing.T) {
		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusPending,
			PaymentMethod: "CARD",
		}

		createdTransaction, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID, anotherPaidCourse.ID})

		assert.NoError(t, err)
		assert.NotZero(t, createdTransaction.ID)
		assert.Equal(t, student.ID, createdTransaction.StudentID)
		assert.Equal(t, models.TransactionStatusPending, createdTransaction.Status)
		assert.Equal(t, "CARD", createdTransaction.PaymentMethod)
		// 1000 - 10% + 2000 - 20% = 900 + 1600 = 2500
		assert.Equal(t, 2500, createdTransaction.Amount)
		assert.Len(t, createdTransaction.Courses, 2)
	})

	t.Run("Create transaction with invalid course", func(t *testing.T) {
		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusPending,
			PaymentMethod: "UPI",
		}

		_, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{99999})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "some courses not found")
	})

	t.Run("Create transaction with free course should fail", func(t *testing.T) {
		// Create a free course
		freeCourse := models.Course{
			Name:        "Test Transaction Free Course",
			Description: "A free course for transaction testing",
			Price:       0,
			IsFree:      true,
			CourseType:  models.CourseTypeIITJEE,
		}
		testDB.Create(&freeCourse)

		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusPending,
			PaymentMethod: "UPI",
		}

		_, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{freeCourse.ID})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot create transaction for free course")
	})

	t.Run("Create transaction with invalid status", func(t *testing.T) {
		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        "INVALID_STATUS",
			PaymentMethod: "UPI",
		}

		_, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID})

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid transaction status")
	})
}

func TestUpdateTransactionStatus(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, _ := createTestCourses(t)

	// Create a transaction first
	transaction := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusPending,
		PaymentMethod: "UPI",
	}

	createdTransaction, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID})
	require.NoError(t, err)

	t.Run("Update transaction status to completed", func(t *testing.T) {
		err := dbPlugin.UpdateTransactionStatus(ctx, createdTransaction.ID, models.TransactionStatusCompleted, "txn_123456")

		assert.NoError(t, err)

		// Verify the update
		updatedTransaction, err := dbPlugin.GetTransactionByID(ctx, createdTransaction.ID)
		assert.NoError(t, err)
		assert.Equal(t, models.TransactionStatusCompleted, updatedTransaction.Status)
		assert.Equal(t, "txn_123456", updatedTransaction.PaymentReference)
	})

	t.Run("Update transaction status to failed", func(t *testing.T) {
		err := dbPlugin.UpdateTransactionStatus(ctx, createdTransaction.ID, models.TransactionStatusFailed, "")

		assert.NoError(t, err)

		// Verify the update
		updatedTransaction, err := dbPlugin.GetTransactionByID(ctx, createdTransaction.ID)
		assert.NoError(t, err)
		assert.Equal(t, models.TransactionStatusFailed, updatedTransaction.Status)
	})

	t.Run("Update non-existent transaction", func(t *testing.T) {
		err := dbPlugin.UpdateTransactionStatus(ctx, 99999, models.TransactionStatusCompleted, "")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "transaction not found")
	})

	t.Run("Update with invalid status", func(t *testing.T) {
		err := dbPlugin.UpdateTransactionStatus(ctx, createdTransaction.ID, "INVALID_STATUS", "")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid transaction status")
	})
}

func TestGetStudentTransactions(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, anotherPaidCourse := createTestCourses(t)

	// Create multiple transactions
	transaction1 := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusCompleted,
		PaymentMethod: "UPI",
	}
	_, err := dbPlugin.CreateTransaction(ctx, transaction1, []uint{paidCourse.ID})
	require.NoError(t, err)

	transaction2 := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusPending,
		PaymentMethod: "CARD",
	}
	_, err = dbPlugin.CreateTransaction(ctx, transaction2, []uint{anotherPaidCourse.ID})
	require.NoError(t, err)

	t.Run("Get all transactions for student", func(t *testing.T) {
		transactions, err := dbPlugin.GetStudentTransactions(ctx, student.ID)

		assert.NoError(t, err)
		assert.Len(t, transactions, 2)

		// Verify transactions are ordered by date (newest first)
		assert.True(t, transactions[0].TransactionDate.After(transactions[1].TransactionDate) ||
			transactions[0].TransactionDate.Equal(transactions[1].TransactionDate))

		// Verify courses are loaded
		for _, transaction := range transactions {
			assert.NotEmpty(t, transaction.Courses)
		}
	})

	t.Run("Get transactions for non-existent student", func(t *testing.T) {
		transactions, err := dbPlugin.GetStudentTransactions(ctx, 99999)

		assert.NoError(t, err)
		assert.Empty(t, transactions)
	})
}

func TestEnrollStudentInPurchasedCourses(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, anotherPaidCourse := createTestCourses(t)

	// Create a completed transaction
	transaction := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusCompleted,
		PaymentMethod: "UPI",
	}
	createdTransaction, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID, anotherPaidCourse.ID})
	require.NoError(t, err)

	t.Run("Auto-enroll student in purchased courses", func(t *testing.T) {
		err := dbPlugin.EnrollStudentInPurchasedCourses(ctx, createdTransaction.ID)

		assert.NoError(t, err)

		// Verify student is enrolled in both courses
		var enrolledStudent models.Student
		testDB.Preload("Courses").First(&enrolledStudent, student.ID)

		assert.Len(t, enrolledStudent.Courses, 2)
		courseIDs := make([]uint, len(enrolledStudent.Courses))
		for i, course := range enrolledStudent.Courses {
			courseIDs[i] = course.ID
		}
		assert.Contains(t, courseIDs, paidCourse.ID)
		assert.Contains(t, courseIDs, anotherPaidCourse.ID)
	})

	t.Run("Auto-enroll with pending transaction should fail", func(t *testing.T) {
		// Create pending transaction
		pendingTransaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusPending,
			PaymentMethod: "CARD",
		}
		createdPendingTransaction, err := dbPlugin.CreateTransaction(ctx, pendingTransaction, []uint{paidCourse.ID})
		require.NoError(t, err)

		err = dbPlugin.EnrollStudentInPurchasedCourses(ctx, createdPendingTransaction.ID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot enroll: transaction status is PENDING")
	})

	t.Run("Auto-enroll with non-existent transaction", func(t *testing.T) {
		err := dbPlugin.EnrollStudentInPurchasedCourses(ctx, 99999)

		assert.Error(t, err)
	})
}

func TestEnrollStudentInCourseWithTransactionValidation(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, _ := createTestCourses(t)

	// Create free course
	freeCourse := models.Course{
		Name:        "Test Transaction Free Course",
		Description: "A free course for enrollment testing",
		Price:       0,
		IsFree:      true,
		CourseType:  models.CourseTypeNEET,
	}
	testDB.Create(&freeCourse)

	t.Run("Enroll in free course without transaction", func(t *testing.T) {
		enrolledStudent, err := dbPlugin.EnrollStudentInCourse(ctx, student.User.ID, freeCourse.ID)

		assert.NoError(t, err)
		assert.NotNil(t, enrolledStudent)

		// Verify enrollment
		var studentWithCourses models.Student
		testDB.Preload("Courses").First(&studentWithCourses, student.ID)
		assert.Len(t, studentWithCourses.Courses, 1)
		assert.Equal(t, freeCourse.ID, studentWithCourses.Courses[0].ID)
	})

	t.Run("Enroll in paid course without transaction should fail", func(t *testing.T) {
		_, err := dbPlugin.EnrollStudentInCourse(ctx, student.User.ID, paidCourse.ID)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no valid transaction found for paid course")
	})

	t.Run("Enroll in paid course with completed transaction", func(t *testing.T) {
		// Create completed transaction
		transaction := &models.Transaction{
			StudentID:     student.ID,
			Status:        models.TransactionStatusCompleted,
			PaymentMethod: "UPI",
		}
		_, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID})
		require.NoError(t, err)

		enrolledStudent, err := dbPlugin.EnrollStudentInCourse(ctx, student.User.ID, paidCourse.ID)

		assert.NoError(t, err)
		assert.NotNil(t, enrolledStudent)

		// Verify enrollment
		var studentWithCourses models.Student
		testDB.Preload("Courses").First(&studentWithCourses, student.ID)

		// Should have both free course and paid course now
		assert.Len(t, studentWithCourses.Courses, 2)
		courseIDs := make([]uint, len(studentWithCourses.Courses))
		for i, course := range studentWithCourses.Courses {
			courseIDs[i] = course.ID
		}
		assert.Contains(t, courseIDs, freeCourse.ID)
		assert.Contains(t, courseIDs, paidCourse.ID)
	})

	t.Run("Enroll in already enrolled course", func(t *testing.T) {
		// Try to enroll again in the same paid course
		enrolledStudent, err := dbPlugin.EnrollStudentInCourse(ctx, student.User.ID, paidCourse.ID)

		assert.NoError(t, err)
		assert.NotNil(t, enrolledStudent)

		// Should still have only 2 courses (no duplicates)
		var studentWithCourses models.Student
		testDB.Preload("Courses").First(&studentWithCourses, student.ID)
		assert.Len(t, studentWithCourses.Courses, 2)
	})
}

func TestGetTransactionByID(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, _ := createTestCourses(t)

	// Create a transaction
	transaction := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusCompleted,
		PaymentMethod: "UPI",
	}
	createdTransaction, err := dbPlugin.CreateTransaction(ctx, transaction, []uint{paidCourse.ID})
	require.NoError(t, err)

	t.Run("Get existing transaction", func(t *testing.T) {
		retrievedTransaction, err := dbPlugin.GetTransactionByID(ctx, createdTransaction.ID)

		assert.NoError(t, err)
		assert.Equal(t, createdTransaction.ID, retrievedTransaction.ID)
		assert.Equal(t, student.ID, retrievedTransaction.StudentID)
		assert.NotNil(t, retrievedTransaction.Student)
		assert.NotNil(t, retrievedTransaction.Student.User)
		assert.NotEmpty(t, retrievedTransaction.Courses)
		assert.Equal(t, paidCourse.ID, retrievedTransaction.Courses[0].ID)
	})

	t.Run("Get non-existent transaction", func(t *testing.T) {
		_, err := dbPlugin.GetTransactionByID(ctx, 99999)

		assert.Error(t, err)
	})
}

func TestGetCompletedTransactionsByStudent(t *testing.T) {
	ctx := context.Background()
	student := createTestStudent(t)
	paidCourse, anotherPaidCourse := createTestCourses(t)

	// Create completed transaction
	completedTransaction := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusCompleted,
		PaymentMethod: "UPI",
	}
	_, err := dbPlugin.CreateTransaction(ctx, completedTransaction, []uint{paidCourse.ID})
	require.NoError(t, err)

	// Create pending transaction
	pendingTransaction := &models.Transaction{
		StudentID:     student.ID,
		Status:        models.TransactionStatusPending,
		PaymentMethod: "CARD",
	}
	_, err = dbPlugin.CreateTransaction(ctx, pendingTransaction, []uint{anotherPaidCourse.ID})
	require.NoError(t, err)

	t.Run("Get only completed transactions", func(t *testing.T) {
		transactions, err := dbPlugin.GetCompletedTransactionsByStudent(ctx, student.ID)

		assert.NoError(t, err)
		assert.Len(t, transactions, 1)
		assert.Equal(t, models.TransactionStatusCompleted, transactions[0].Status)
		assert.NotEmpty(t, transactions[0].Courses)
	})

	t.Run("Get completed transactions for non-existent student", func(t *testing.T) {
		transactions, err := dbPlugin.GetCompletedTransactionsByStudent(ctx, 99999)

		assert.NoError(t, err)
		assert.Empty(t, transactions)
	})
}
